import {
    Controller,
    Get,
    Param,
    Query,
    BadRequestException,
} from "@nestjs/common";
import { DatabaseService } from "../services/database.service";
import {
    IssueListItemDto,
    IssueDetailsResponseDto,
    GroupedStatisticsDto,
    DateRangeQueryDto,
    StatisticIssuesResponseDto,
    StatisticType,
    STATISTIC_TYPES,
} from "../dto";

@Controller("issues")
export class IssuesController {
    constructor(private readonly databaseService: DatabaseService) {}

    @Get()
    async getIssuesList(
        @Query() q: DateRangeQueryDto
    ): Promise<IssueListItemDto[]> {
        const start = q.startDate ? new Date(q.startDate) : null;
        const end = q.endDate ? new Date(q.endDate) : null;
        return await this.databaseService.getIssuesList(start, end);
    }

    @Get("statistics")
    async getStatistics(
        @Query() q: DateRangeQueryDto
    ): Promise<GroupedStatisticsDto> {
        const start = new Date(q.startDate);
        const end = new Date(q.endDate);
        return await this.databaseService.getStatistics(start, end);
    }

    @Get("statistics/:slug")
    async getStatisticIssues(
        @Param("slug") slug: StatisticType,
        @Query() q: DateRangeQueryDto
    ): Promise<StatisticIssuesResponseDto> {
        // Validate that the slug is a valid statistic type
        if (!STATISTIC_TYPES.includes(slug)) {
            throw new BadRequestException(
                `Invalid statistic type: ${slug}. Must be one of: ${STATISTIC_TYPES.join(", ")}`
            );
        }

        const start = q.startDate ? new Date(q.startDate) : null;
        const end = q.endDate ? new Date(q.endDate) : null;

        // TODO: Implement database query to get issues for specific statistic
        return await this.databaseService.getStatisticIssues(slug, start, end);
    }

    @Get(":issueKey")
    async getIssueDetails(
        @Param("issueKey") issueKey: string
    ): Promise<IssueDetailsResponseDto> {
        return await this.databaseService.getIssueDetails(issueKey);
    }
}
