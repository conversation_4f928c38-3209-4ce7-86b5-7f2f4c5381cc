import {
    IsDateString,
    IsO<PERSON>al,
    IsIn,
    Validate,
    ValidatorConstraint,
    ValidatorConstraintInterface,
    ValidationArguments,
} from "class-validator";

interface DateRangeObject {
    startDate?: string;
    endDate?: string;
}

@ValidatorConstraint({ name: "DateRangeOrder", async: false })
class DateRangeOrder implements ValidatorConstraintInterface {
    validate(value: unknown, args: ValidationArguments): boolean {
        const obj = args.object as DateRangeObject;
        if (obj.startDate && obj.endDate) {
            return (
                new Date(obj.startDate).getTime() <=
                new Date(obj.endDate).getTime()
            );
        }
        return true;
    }

    defaultMessage(): string {
        return "startDate must be before or equal to endDate";
    }
}

export class DateRangeQueryDto {
    @IsOptional()
    @IsDateString()
    startDate: string;

    @IsOptional()
    @IsDateString()
    endDate: string;

    // Class-level validation to ensure proper ordering when both are provided
    @Validate(DateRangeOrder)
    private readonly _orderCheck?: unknown;
}

/**
 * Available statistic types in the system
 */
export const STATISTIC_TYPES = [
    "avg_time_for_code_review",
    "avg_time_for_bug_for_qa_to_todo",
    "avg_time_for_testing",
] as const;

export type StatisticType = (typeof STATISTIC_TYPES)[number];

export class StatisticSlugDto {
    @IsIn(STATISTIC_TYPES, {
        message: `Statistic type must be one of: ${STATISTIC_TYPES.join(", ")}`,
    })
    slug: StatisticType;
}
